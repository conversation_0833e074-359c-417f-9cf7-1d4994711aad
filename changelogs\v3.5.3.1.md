# What's Changed

> 📢 在升级前，请完整阅读本次更新日志。
> 此版本为针对 `v3.5.3` 的紧急修复版本

## ✨ 新增的功能

1. Telegram、Webchat、QQ官方机器人平台(私聊)支持流式输出（实验性）。@Soulter @Raven95676 @anka-afk
2. 支持针对不同消息平台开启/关闭插件 @zhx8702 @Raven95676 @Soulter
3. 插件市场支持显示 Star 个数、插件管理支持插件帮助对话框 @kterna
4. 飞书平台支持主动消息发送 @Soulter
5. Telegram 平台适配显示指令列表，支持自动补全 @Raven95676
6. 新增配置项允许配置当超出最多携带对话数量时，一次性丢弃多少条旧消息 @Rail1bc
7. StarTool 新增获取插件数据目录接口 @Raven95676

## 🎈 功能性优化

1. 优化 /his 指令对函数调用的显示 @anka-afk
2. QQ 官方机器人支持对同一条消息多次回复 @kuangfeng

## 🐛 修复的 Bug

1. ‼️ 修复使用 gemini 时，函数数工具调用会重复调用已经在过去会话中调用过的工具 @Soulter
2. 修复使用 Gemini 模型时出现 <empty_content> 的问题 @anka-afk
4. 修复使用 OneAPI + Gemini(openai) 传递空参数函数工具时可能报错的问题 @Soulter
5. 修复 permission 过滤算子的 raise_error 参数失效的问题 @Soulter
6. 修复函数调用时可能出现 `messages with role 'tool' must be a response to a preceeding message with 'tool_calls'` 报错的问题 @anka-afk
7. 修复 dify 下删除对话的报错问题 @Soulter
8. 修复人格预设对话多次插入上下文的问题 @Rail1bc
9. 修复了 event.get_sender_id() 返回值与函数注释不一致的问题 @zsbai


## 🧩 新增的插件

待补充
