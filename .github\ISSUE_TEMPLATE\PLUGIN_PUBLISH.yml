name: '🥳 发布插件'
title: "[Plugin] 插件名"
description: 提交插件到插件市场
labels: [ "plugin-publish" ]
body:
  - type: markdown
    attributes:
      value: |
        欢迎发布插件到插件市场！请确保您的插件经过**完整的**测试。
  
  - type: textarea
    attributes:
      label: 插件仓库
      description: 插件的 GitHub 仓库链接
      placeholder: >
        如 https://github.com/Soulter/astrbot-github-cards

  - type: textarea
    attributes:
      label: 描述
      value: |
        插件名: 
        插件作者: 
        插件简介: 
        支持的消息平台:(必填，如 QQ、微信、飞书)
        标签:(可选)
        社交链接:(可选, 将会在插件市场作者名称上作为可点击的链接)
      description: 必填。请以列表的字段按顺序将插件名、插件作者、插件简介放在这里。如果您不知道支持哪些消息平台，请填写测试过的消息平台。

  - type: checkboxes
    attributes:
      label: Code of Conduct
      options:
        - label: >
            我已阅读并同意遵守该项目的 [行为准则](https://docs.github.com/zh/site-policy/github-terms/github-community-code-of-conduct)。
          required: true

  - type: markdown
    attributes:
      value: "❤️"
