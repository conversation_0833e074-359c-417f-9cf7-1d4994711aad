on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:

name: Auto Release

jobs:
  build-and-publish-to-github-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Dashboard Build
        run: |
          cd dashboard
          npm install
          npm run build
          echo "COMMIT_SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV
          echo ${{ github.ref_name }} > dist/assets/version
          zip -r dist.zip dist
      
      - name: Upload to Cloudflare R2
        env:
          R2_ACCOUNT_ID: ${{ secrets.R2_ACCOUNT_ID }}
          R2_ACCESS_KEY_ID: ${{ secrets.R2_ACCESS_KEY_ID }}
          R2_SECRET_ACCESS_KEY: ${{ secrets.R2_SECRET_ACCESS_KEY }}
          R2_BUCKET_NAME: "astrbot"
          R2_OBJECT_NAME: "astrbot-webui-latest.zip"
          VERSION_TAG: ${{ github.ref_name }}
        run: |
          echo "Installing rclone..."
          curl https://rclone.org/install.sh | sudo bash

          echo "Configuring rclone remote..."
          mkdir -p ~/.config/rclone
          cat <<EOF > ~/.config/rclone/rclone.conf
          [r2]
          type = s3
          provider = Cloudflare
          access_key_id = $R2_ACCESS_KEY_ID
          secret_access_key = $R2_SECRET_ACCESS_KEY
          endpoint = https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com
          EOF

          echo "Uploading dist.zip to R2 bucket: $R2_BUCKET_NAME/$R2_OBJECT_NAME"
          mv dashboard/dist.zip dashboard/$R2_OBJECT_NAME
          rclone copy dashboard/$R2_OBJECT_NAME r2:$R2_BUCKET_NAME --progress
          mv dashboard/$R2_OBJECT_NAME dashboard/astrbot-webui-${VERSION_TAG}.zip
          rclone copy dashboard/astrbot-webui-${VERSION_TAG}.zip r2:$R2_BUCKET_NAME --progress
          mv dashboard/astrbot-webui-${VERSION_TAG}.zip dashboard/dist.zip

      - name: Fetch Changelog
        run: |
          echo "changelog=changelogs/${{github.ref_name}}.md" >> "$GITHUB_ENV"

      - name: Create GitHub Release
        uses: ncipollo/release-action@v1
        with:
          bodyFile: ${{ env.changelog }}
          artifacts: "dashboard/dist.zip"
      
  build-and-publish-to-pypi:
    # 构建并发布到 PyPI
    runs-on: ubuntu-latest
    needs: build-and-publish-to-github-release
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install uv
        run: |
          python -m pip install uv

      - name: Build package
        run: |
          uv build

      - name: Publish to PyPI
        env:
          UV_PUBLISH_TOKEN: ${{ secrets.PYPI_TOKEN }}
        run: |
          uv publish
