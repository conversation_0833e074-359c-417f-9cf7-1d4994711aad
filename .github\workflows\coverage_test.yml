name: Run tests and upload coverage

on: 
  push:
    branches:
      - master
    paths-ignore:
      - 'README.md'
      - 'changelogs/**'
      - 'dashboard/**'
  workflow_dispatch:

jobs:
  test:
    name: Run tests and collect coverage
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov pytest-asyncio

      - name: Run tests
        run: |
          mkdir data
          mkdir data/plugins
          mkdir data/config
          mkdir data/temp
          export TESTING=true
          export ZHIPU_API_KEY=${{ secrets.OPENAI_API_KEY }}
          PYTHONPATH=./ pytest --cov=. tests/ -v -o log_cli=true -o log_level=DEBUG

      - name: Upload results to Codecov
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}