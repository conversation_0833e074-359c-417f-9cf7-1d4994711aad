# What's Changed

1. fix: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand. #396
2. remove: 移除了 put_history_to_prompt。当主动回复时，将群聊记录将自动放入prompt，当未主动回复但是开启群聊增强时，群聊记录将放入system prompt
3. fix: 插件错误信息点击关闭没反应 #394
4. fix: 自部署文转图不生效 #352
5. fix: Google Search 报 429 错误时，放宽 Exception 至其他搜索引擎 #405
6. fix: 使用 Google Gemini （OpenAI 兼容）的部分情况下联网搜索等函数调用工具没被调用 #342
7. fix: 修复尝试弹出最早的记录失效的问题
8. fix: 移除了分段回复llm提示词辅助
9. perf: 当图片数据为空时不加入上下文 #379
10. 修复 dify 返回的结果带有多行数据时的 json 解析异常导致返回值为空的问题 #298 by @zhaolj