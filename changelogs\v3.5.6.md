# What's Changed

> 🙁 Gewechat 已经停止维护，我们将更换更稳定的个人微信接入方式。如有问题请提交 issue。
> 🧐 预告：接下来三个版本之内将会逐步上线 Live2D 桌宠、长期记忆（实验性）的功能。

1. Gewechat 相关 bug 修复（即使已经不可用 :( ） @BigFace123 @XiGuang @Soulter
2. 支持 CLI 命令行 @LIghtJUNction
3. 修复 QQ 下带有网址的指令可能无法识别的问题 @kkjzio
4. `reset` 指令优化 @anka-afk
5. Gemini 请求优化，支持 Gemini 思考信息设置 @Raven95676
6. 支持处理 MCP 服务器返回的图片等多模态信息 @Raven95676
7. 插件市场支持基于 Star 和 更新时间排序 @Soulter
8. 优化 QQ 下自动下载文件导致磁盘被占满的问题 @Soulter @anka-afk